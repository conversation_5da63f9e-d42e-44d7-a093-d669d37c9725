import { useCollapsed } from 'app/providers/CollapsedProvider'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { ButtonMailingReport } from 'features/ButtonMailingReport'
import { observer } from 'mobx-react'
import cls from 'pages/CalculationsPage/ui/CalculationsPage.module.scss'
import { ActionButton } from 'pages/CalculationsPage/ui/VaultBody/ui/ActionButton'
import { AVRCHMSpreadsheet } from 'pages/CalculationsPage/ui/VaultBody/ui/AVRCHMSpreadsheet/AVRCHMSpreadsheet.tsx'
import { VaultSpreadsheet } from 'pages/CalculationsPage/ui/VaultBody/ui/VaultSpreadsheet.tsx'
import { useEffect, useRef, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { AccessControl } from 'shared/ui/AccessControl'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { useStore } from 'stores/useStore.ts'

import { ModalUpdateVault } from './ui/ModalUpdateVault'
import { Zones } from './ui/Zones'

export const VaultBody = observer(() => {
  const { calculationsPageStore } = useStore()
  const { vaultStore, isLastDay, viewOnly } = calculationsPageStore
  const { vaultLoadDataStatus, save, isModalUpdateVault, isEditRows, resetData } = vaultStore

  const { isAnimating } = useCollapsed()

  const mainContainerRef = useRef<HTMLDivElement | null>(null)
  const [containerWidth, setContainerWidth] = useState<number | undefined>()

  // Функция для расчета ширины с учетом отступа до вертикального скролла
  const calculateWidth = (): number | undefined => {
    if (!mainContainerRef.current) return undefined
    const LEFT_SCROLLBAR_GAP = 6

    return mainContainerRef.current.clientWidth - LEFT_SCROLLBAR_GAP
  }

  /*
    Обработчик изменения размера контейнера.
    Он сработает и при ресайзе окна, и при других изменениях лэйаута.
  */
  useEffect(() => {
    // Не создаем observer, пока идет анимация
    if (isAnimating || !mainContainerRef.current) return

    const observer = new ResizeObserver(() => {
      requestAnimationFrame(() => {
        // Дополнительная проверка, чтобы не обновлять стейт во время анимации
        if (!isAnimating) {
          const width = calculateWidth()
          setContainerWidth(width)
        }
      })
    })

    observer.observe(mainContainerRef.current)

    return () => observer.disconnect()
  }, [isAnimating])

  useHotkeys('ctrl+shift+s', () => !viewOnly && isEditRows && !isLastDay && save(), {
    enableOnFormTags: true,
  })
  useHotkeys('ctrl+shift+x', () => !viewOnly && isEditRows && !isLastDay && resetData(), {
    enableOnFormTags: true,
  })

  return (
    <div ref={mainContainerRef} className={cls.main}>
      <SubtitleWithActions
        isActionsVisible
        title={
          <div className={cls.HeaderCustomWithButton}>
            <h2 className={cls.TitleVault}>Свод</h2>
          </div>
        }
        className={cls.title}
        actions={[
          <AccessControl key={ROLES.TECHNOLOGIST} rules={[ROLES.TECHNOLOGIST]}>
            <ActionButton.PlantsActions />
            <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
              <ActionButton.SetFixing />
              <ActionButton.ResetFixing />
            </div>
            <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
              <ActionButton.LoadAll />
              <ActionButton.LoadISP />
              <ActionButton.LoadMODES />
              <ActionButton.LoadCM />
            </div>
            <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
              {isOptimization && <ActionButton.DoOptimization />}
              <ActionButton.CalculateAllowedZones />
              <ActionButton.CalculateGeneration />
              <ActionButton.EnteringAllowedZones />
              <ActionButton.EnteringAllowedZonesToBounds />
              <ActionButton.BalanceRGU />
              <ActionButton.CalculateGenerationMaximum />
            </div>
            <div className={cls.ButtonContainer}>
              <ActionButton.AVRCHMLoadButton />
            </div>
            <ActionButton.Reset />
            <ActionButton.Save />
            <div className={cls.ButtonContainer}>
              <ActionButton.Accept />
            </div>
            <div className={classNames(cls.ButtonContainer, {}, [cls.RightGap])}>
              <ActionButton.Disaccept />
            </div>
            <ButtonMailingReport plantId={0} />
          </AccessControl>,
        ]}
      />
      <div className={cls.Up} style={{ width: containerWidth ? `${containerWidth}px` : '100%' }}>
        <VaultSpreadsheet />
      </div>
      <div className={cls.Middle}>
        <Zones />
      </div>
      {vaultLoadDataStatus === 'DONE' && (
        <div className={cls.DownContainer} style={{ width: containerWidth ? `${containerWidth}px` : '100%' }}>
          <AVRCHMSpreadsheet />
        </div>
      )}
      {isModalUpdateVault && <ModalUpdateVault />}
    </div>
  )
})
