import 'handsontable/dist/handsontable.full.min.css'
import '../Spreadsheet/ui/Spreadsheet.scss'

import { HotTable, HotTableClass, HotTableProps } from '@handsontable/react'
import Handsontable from 'handsontable'
import { registerAllModules } from 'handsontable/registry'
import { HyperFormula } from 'hyperformula'
import { forwardRef, RefCallback, useEffect, useLayoutEffect, useRef } from 'react'
import { getShortcutForEditor, getShortcutForGrid, SHORTCUT_GROUP_ID } from 'widgets/Spreadsheet/ui/lib'

import { collapseColumnsAfterInit, setColumnCollapse } from './lib'
import { ExpandedDetailedSettings } from './types'

// Регистрируем модули один раз при импорте компонента
registerAllModules()

type PartialHotTableProps = Omit<HotTableProps, 'settings' | 'collapsibleColumns'>

export type SpreadsheetReactProps = PartialHotTableProps & {
  collapsibleColumns?: ExpandedDetailedSettings[]
  showCollapseAll?: boolean
  defaultCollapsed?: boolean
  beforeCancelChange?: PartialHotTableProps['beforeChange']
  enableFormulasPlugin?: boolean
}

// Настройки по умолчанию
const defaultSettings: Partial<HotTableProps> = {
  licenseKey: 'non-commercial-and-evaluation',
  rowHeaders: true,
  colHeaders: true,
  height: 'auto',
  width: 'auto',
  comments: true,
  autoRowSize: true,
  className: 'spreadsheet spreadsheetSm',
}
const defaultCollapsibleColumns: ExpandedDetailedSettings[] = []

export const SpreadsheetReact = forwardRef<HotTableClass, SpreadsheetReactProps>((props, ref) => {
  const {
    data,
    cell,
    showCollapseAll,
    collapsibleColumns = defaultCollapsibleColumns,
    nestedHeaders,
    enableFormulasPlugin,
    ...restProps
  } = props
  const internalRef = useRef<HotTableClass | null>(null)
  const toggleAllRef = useRef<HTMLElement | null>(null)
  const headersMap = useRef<number[]>([])
  /** для отслеживания свернутых столбцов */
  const collapsedCols = useRef<number[]>([])
  /** для раскрытия всех колонок при обновлении данных */
  const isExpandAllRef = useRef(false)
  /** для кнопки "свернуть/развернуть все" */
  const isAllCollapsedRef = useRef(true)
  let cursor: number = 0

  const toggleAllHeaders = (_: Event) => {
    if (internalRef.current) {
      const collapsibleColumnsPlugin = internalRef.current.hotInstance?.getPlugin('collapsibleColumns')

      if (isAllCollapsedRef.current) {
        isAllCollapsedRef.current = false
        collapsibleColumnsPlugin?.expandAll()
      } else {
        isAllCollapsedRef.current = true
        collapsibleColumnsPlugin?.collapseAll()
      }
    }
  }

  const beforeChange: Handsontable.GridSettings['beforeChange'] = (changes, source) => {
    const cancelChange = changes.some((cellChange) => {
      if (cellChange === null) return true
      const [, , , newValue] = cellChange

      return String(newValue).includes('=')
    })
    restProps?.beforeCancelChange?.(changes, source)
    restProps?.beforeChange?.(changes, source)

    return !cancelChange
  }

  /**
   * Обработчик автозаполнения (перетягивания квадратика заполнения).
   * Предотвращает копирование значений в ячейки, которые не редактируются.
   */
  const beforeAutofill: Handsontable.GridSettings['beforeAutofill'] = (
    selectionData,
    sourceRange,
    targetRange,
    deltas,
  ) => {
    // Сначала вызываем пользовательский beforeAutofill, если он есть
    let processedData: any[][] = restProps?.beforeAutofill
      ? (restProps.beforeAutofill(selectionData, sourceRange, targetRange, deltas) as any[][]) || selectionData
      : selectionData

    // Проверяем каждую ячейку в целевом диапазоне
    const filteredData = processedData.map((row: any[], rowIndex: number) => {
      return row.map((cellValue: any, colIndex: number) => {
        const targetRow = targetRange.from.row + rowIndex
        const targetCol = targetRange.from.col + colIndex

        // Получаем настройки ячейки из cell prop
        const cellProps = cell?.find((c: any) => c.row === targetRow && c.col === targetCol)

        // Проверяем, является ли ячейка редактируемой
        const isReadOnly = cellProps?.readOnly === true
        const hasNoEditor = cellProps?.editor === false
        const isDisabled = String(cellProps?.className || '')
          .toLowerCase()
          .includes('disabled')

        // Если ячейка не редактируется, возвращаем пустое значение
        if (isReadOnly || hasNoEditor || isDisabled) {
          return null
        }

        return cellValue
      })
    })

    return filteredData
  }

  const afterGetColHeader: Handsontable.GridSettings['afterGetColHeader'] = (col, th, level) => {
    if (col === -1 && level === 0 && collapsibleColumns?.length) {
      if (showCollapseAll) {
        th.innerHTML = `<div class="relative">
                          <span class="colHeader">&nbsp</span>
                          <div class="collapsibleIndicator" style='left: calc(50% - 6px) !important;'>
                            ${isAllCollapsedRef.current ? '+' : '-'}
                          </div>
                        </div>`

        if (toggleAllRef.current) {
          toggleAllRef.current.removeEventListener('click', toggleAllHeaders)
        }
        toggleAllRef.current = th
        toggleAllRef.current.addEventListener('click', toggleAllHeaders)
      }

      return
    }

    restProps?.afterGetColHeader?.(col, th, level)
  }

  useEffect(() => {
    if (nestedHeaders && nestedHeaders?.length > 0) {
      nestedHeaders[0]?.forEach((el, index) => {
        if (typeof el !== 'string') {
          const colspan = el.colspan
          for (let i = 0; i < colspan; i++) {
            headersMap.current[cursor] = index
            cursor = cursor + 1
          }
        }
      })
    }
  }, [nestedHeaders])

  useEffect(() => {
    const hotInstance = internalRef.current?.hotInstance
    if (hotInstance) {
      const gridContext = hotInstance.getShortcutManager().getContext('grid')
      const editorContext = hotInstance.getShortcutManager().getContext('editor')

      gridContext?.addShortcut(getShortcutForGrid(hotInstance))
      editorContext?.addShortcut(getShortcutForEditor(hotInstance))
      // хук вызывается до окончательного уничтожения инстанса таблицы
      hotInstance.addHook('afterDestroy', () => {
        const gridContext = hotInstance.getShortcutManager().getContext('grid')
        const editorContext = hotInstance.getShortcutManager().getContext('editor')
        gridContext?.removeShortcutsByGroup(SHORTCUT_GROUP_ID)
        editorContext?.removeShortcutsByGroup(SHORTCUT_GROUP_ID)
      })

      // изначально сворачиваем все развернутые колонки
      if (collapsibleColumns.length) {
        collapsedCols.current = collapseColumnsAfterInit(hotInstance, collapsibleColumns, headersMap.current)
      }
    }

    return () => {
      if (toggleAllRef.current) {
        toggleAllRef.current.removeEventListener('click', toggleAllHeaders)
      }
    }
  }, [])

  const setRef: RefCallback<HotTableClass> = (hotRef) => {
    internalRef.current = hotRef
    if (typeof ref === 'function') {
      ref(hotRef)
    } else if (ref?.current) {
      ref.current = hotRef
    }
  }

  useLayoutEffect(() => {
    const hotInstance = internalRef.current?.hotInstance
    if (hotInstance) {
      hotInstance.batch(() => {
        if (collapsibleColumns.length) {
          isExpandAllRef.current = true
          hotInstance.getPlugin('collapsibleColumns').expandAll()
          isExpandAllRef.current = false
        }

        // после обновления восстанавливаем состояние свернутых колонок
        if (collapsibleColumns.length) {
          // нужно для корректной работы обновления данных при наличии collapsibleColumns
          if (data) {
            hotInstance.updateData(data)
          }

          const newCollapsibleColumns: ExpandedDetailedSettings[] = []
          collapsedCols.current.forEach((col: number) => {
            const columnIdx = headersMap.current.findIndex((nestedHeaderIdx) => nestedHeaderIdx === col)

            if (columnIdx !== -1) {
              const collapsibleColumn = collapsibleColumns.find((item) => item.col === columnIdx)

              if (collapsibleColumn) {
                newCollapsibleColumns.push({ row: collapsibleColumn.row, col: columnIdx, collapsible: true })
              }
            }
          })

          collapsedCols.current = collapseColumnsAfterInit(hotInstance, newCollapsibleColumns, headersMap.current)
        }
      })
    }
  }, [data, internalRef.current])

  return (
    <HotTable
      ref={setRef}
      formulas={
        enableFormulasPlugin
          ? {
              engine: HyperFormula,
            }
          : undefined
      }
      {...defaultSettings}
      {...restProps}
      {...(collapsibleColumns.length === 0 ? { data } : {})}
      afterGetColHeader={afterGetColHeader}
      beforeChange={beforeChange}
      beforeAutofill={beforeAutofill}
      afterColumnCollapse={(...args) =>
        setColumnCollapse(...args, headersMap.current, collapsedCols, isExpandAllRef.current)
      }
      afterColumnExpand={(...args) =>
        setColumnCollapse(...args, headersMap.current, collapsedCols, isExpandAllRef.current)
      }
      nestedHeaders={nestedHeaders}
      // отключаем плагин если нет данных
      collapsibleColumns={collapsibleColumns.length ? collapsibleColumns : false}
      cell={cell}
    />
  )
})

// Для нормального отображения в DevTools
SpreadsheetReact.displayName = 'SpreadsheetReact'
