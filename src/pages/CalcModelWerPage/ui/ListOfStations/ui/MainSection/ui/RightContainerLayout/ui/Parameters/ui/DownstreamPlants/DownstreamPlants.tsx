import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/material'
import { IWerDepartment } from 'entities/api/calcModelWerManager.entities'
import { observer } from 'mobx-react'
import { FC, useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { prepareDate } from 'shared/lib/prepareData'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore'
import { IBaseRowData, IColumn, TableV1 } from 'widgets/TableV1'

import { renderWerDepartments } from '../../lib/renderFuctions'
import { sortFormattedDates } from '../../lib/sortFormattedDates'
import { AddPlantModal } from '../ui/AddPlantModal'
import { UpDownStreamTableCell } from '../ui/UpDownStreamTableCell'
import cls from './DownstreamPlants.module.scss'

interface IDownstreamPlantsRow extends IBaseRowData {
  plantName: string
  travelTime: number | null
  departmentName: IWerDepartment[]
  startDate: string
  endDate: string | null
  affluent?: boolean
  type: string
}

interface Props {
  isEditingEnabled: boolean
}

export const DownstreamPlants: FC<Props> = observer(({ isEditingEnabled }) => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: {
      parametersStore: { plantCascades, updateAffluentValue, editMode, updatePlantDate, removePlant },
    },
  } = calcModelWerStore

  // Функция-костыль для синхронизации рендера таблицы с изменениями в сторе
  const mapPlantData = () =>
    plantCascades?.downstreamPlants?.map((plant) => ({
      tabId: plant.plantId,
      plantName: plant.plantName,
      travelTime: null, // Данные для времени добегания пока бек не дает
      departmentName: plant.werDepartments,
      startDate: prepareDate(plant.startDate),
      endDate: plant.endDate ? prepareDate(plant.endDate) : null,
      affluent: plant.affluent,
      type: 'ADDED', // Костыль, чтобы можно было редактировать startDate
    })) || []

  const initialRows: IDownstreamPlantsRow[] = mapPlantData()

  const [rows, setRows] = useState(initialRows)
  const [isOpenAddNewPlantModal, setIsOpenAddNewPlantModal] = useState(false)

  useEffect(() => {
    setRows(initialRows)
  }, [plantCascades?.downstreamPlants])

  const baseColumns = [
    {
      name: 'plantName',
      title: 'Станция',
      width: 200,
      editingEnabled: false,
      render: (value: IDownstreamPlantsRow['plantName']) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'travelTime',
      title: 'tдоб, дни',
      width: 100,
      editingEnabled: false,
      render: (value: IDownstreamPlantsRow['travelTime']) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'departmentName',
      title: 'ДЦ ВЭР',
      width: 180,
      editingEnabled: false,
      render: renderWerDepartments,
    },
    {
      name: 'startDate',
      title: 'Дата начала связи',
      width: 150,
      editingEnabled: true,
      editType: 'date',
      onAfterChange: (val: IDownstreamPlantsRow['startDate'], row: IDownstreamPlantsRow) => {
        updatePlantDate('downstreamPlants', row.tabId, 'startDate', val)
      },
      render: (value: IDownstreamPlantsRow['startDate']) => <UpDownStreamTableCell value={value} />,
      customSorting: sortFormattedDates,
    },
    {
      name: 'endDate',
      title: 'Дата окончания связи',
      width: 180,
      editingEnabled: true,
      editType: 'date',
      canClearCell: true,
      onAfterChange: (val: IDownstreamPlantsRow['endDate'], row: IDownstreamPlantsRow) => {
        if (val !== null) {
          updatePlantDate('downstreamPlants', row.tabId, 'endDate', val)
        }
      },
      render: (value: IDownstreamPlantsRow['endDate']) => <UpDownStreamTableCell value={value} />,
      customSorting: sortFormattedDates,
    },
    {
      name: 'affluent',
      title: 'Подпор',
      editType: 'switch',
      width: 80,
      editingEnabled: isEditingEnabled,
      onAfterChange: (value: IDownstreamPlantsRow['affluent'], row: IDownstreamPlantsRow) => {
        updateAffluentValue(row.tabId, value as boolean)
      },
    },
  ]

  const actionColumn = {
    name: 'action',
    title: '',
    width: 50,
    editingEnabled: false,
    headRender: () => {
      return (
        <div className={cls.actionHeader}>
          <Tooltip title='Добавить связь'>
            <span>
              <IconButton
                sx={{
                  color: 'var(--primary-color)',
                  display: 'inline-flex!important',
                  padding: 0,
                }}
                className={cls.addIcon}
                onClick={() => {
                  setIsOpenAddNewPlantModal(true)
                }}
                disabled={!isEditingEnabled}
              >
                <Icon name='plus' width={13} />
              </IconButton>
            </span>
          </Tooltip>
        </div>
      )
    },
    render: (_: unknown, row: IDownstreamPlantsRow) => {
      return (
        <div className={cls.actionsWrapper}>
          <div className={cls.iconCell}>
            <Tooltip title='Удалить'>
              <span>
                <IconButton
                  onClick={() => {
                    removePlant('downstreamPlants', row.tabId)
                    setRows(mapPlantData())
                  }}
                  className={classNames('', { [cls.iconRedBtn]: isEditingEnabled })}
                  disabled={!isEditingEnabled}
                >
                  <Icon name='trash' width={13} height={13} />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        </div>
      )
    },
  }

  const columns = (editMode ? [...baseColumns, actionColumn] : baseColumns) as IColumn<IDownstreamPlantsRow>[]

  return (
    <div className={cls.container}>
      <div className={cls.title}>Нижележащая ГЭС</div>
      <TableV1
        setRows={setRows}
        columns={columns}
        rows={rows}
        editMode={isEditingEnabled}
        className={cls.table}
        maxVisibleRows={2}
        rowHeight='auto'
      />
      {isOpenAddNewPlantModal && (
        <AddPlantModal onClose={() => setIsOpenAddNewPlantModal(false)} plantType='downstreamPlants' />
      )}
    </div>
  )
})
