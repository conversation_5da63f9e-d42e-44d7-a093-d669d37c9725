import { describe, expect, it, vi } from 'vitest'
import Handsontable from 'handsontable'

// Mock the Spreadsheet component's beforeAutofill logic
describe('Spreadsheet beforeAutofill functionality', () => {
  const mockColumns = [
    { editor: 'numeric' }, // Editable column
    { editor: false }, // Non-editable column (like LIMITS)
    { editor: 'numeric' }, // Editable column
  ]

  const mockCell = [
    { row: 0, col: 0, readOnly: false, editor: 'numeric' },
    { row: 0, col: 1, readOnly: false, editor: false }, // LIMITS column
    { row: 0, col: 2, readOnly: false, editor: 'numeric' },
    { row: 1, col: 0, readOnly: false, editor: 'numeric' },
    { row: 1, col: 1, readOnly: false, editor: false }, // LIMITS column
    { row: 1, col: 2, readOnly: false, editor: 'numeric' },
  ]

  // Simulate the beforeAutofill logic from our implementation
  const simulateBeforeAutofill = (
    selectionData: any[][],
    targetRange: { from: { row: number; col: number } }
  ) => {
    return selectionData.map((row: any[], rowIndex: number) => {
      return row.map((cellValue: any, colIndex: number) => {
        const targetRow = targetRange.from.row + rowIndex
        const targetCol = targetRange.from.col + colIndex

        // Find cell properties
        const cellProps = mockCell.find(
          (c) => c.row === targetRow && c.col === targetCol
        )

        // Check if cell is editable
        const isReadOnly = cellProps?.readOnly === true
        const hasNoEditor = cellProps?.editor === false
        const isDisabled = String(cellProps?.className || '')
          .toLowerCase()
          .includes('disabled')

        // If cell is not editable, return null
        if (isReadOnly || hasNoEditor || isDisabled) {
          return null
        }

        return cellValue
      })
    })
  }

  it('should allow autofill to editable cells', () => {
    const selectionData = [['100'], ['200']]
    const targetRange = { from: { row: 0, col: 0 } } // Target editable column

    const result = simulateBeforeAutofill(selectionData, targetRange)

    expect(result).toEqual([['100'], ['200']])
  })

  it('should prevent autofill to non-editable cells (LIMITS columns)', () => {
    const selectionData = [['100'], ['200']]
    const targetRange = { from: { row: 0, col: 1 } } // Target LIMITS column (editor: false)

    const result = simulateBeforeAutofill(selectionData, targetRange)

    expect(result).toEqual([[null], [null]])
  })

  it('should handle mixed target ranges (editable and non-editable)', () => {
    const selectionData = [['100', '200', '300']]
    const targetRange = { from: { row: 0, col: 0 } } // Starts from editable, includes LIMITS, ends with editable

    const result = simulateBeforeAutofill(selectionData, targetRange)

    // Should allow first and third columns, but block second (LIMITS)
    expect(result).toEqual([['100', null, '300']])
  })

  it('should prevent autofill to read-only cells', () => {
    const mockCellWithReadOnly = [
      { row: 0, col: 0, readOnly: true, editor: 'numeric' },
    ]

    const simulateWithReadOnly = (
      selectionData: any[][],
      targetRange: { from: { row: number; col: number } }
    ) => {
      return selectionData.map((row: any[], rowIndex: number) => {
        return row.map((cellValue: any, colIndex: number) => {
          const targetRow = targetRange.from.row + rowIndex
          const targetCol = targetRange.from.col + colIndex

          const cellProps = mockCellWithReadOnly.find(
            (c) => c.row === targetRow && c.col === targetCol
          )

          const isReadOnly = cellProps?.readOnly === true
          const hasNoEditor = cellProps?.editor === false

          if (isReadOnly || hasNoEditor) {
            return null
          }

          return cellValue
        })
      })
    }

    const selectionData = [['100']]
    const targetRange = { from: { row: 0, col: 0 } }

    const result = simulateWithReadOnly(selectionData, targetRange)

    expect(result).toEqual([[null]])
  })

  it('should prevent autofill to disabled cells', () => {
    const mockCellWithDisabled = [
      { row: 0, col: 0, readOnly: false, editor: 'numeric', className: 'disabled' },
    ]

    const simulateWithDisabled = (
      selectionData: any[][],
      targetRange: { from: { row: number; col: number } }
    ) => {
      return selectionData.map((row: any[], rowIndex: number) => {
        return row.map((cellValue: any, colIndex: number) => {
          const targetRow = targetRange.from.row + rowIndex
          const targetCol = targetRange.from.col + colIndex

          const cellProps = mockCellWithDisabled.find(
            (c) => c.row === targetRow && c.col === targetCol
          )

          const isReadOnly = cellProps?.readOnly === true
          const hasNoEditor = cellProps?.editor === false
          const isDisabled = String(cellProps?.className || '')
            .toLowerCase()
            .includes('disabled')

          if (isReadOnly || hasNoEditor || isDisabled) {
            return null
          }

          return cellValue
        })
      })
    }

    const selectionData = [['100']]
    const targetRange = { from: { row: 0, col: 0 } }

    const result = simulateWithDisabled(selectionData, targetRange)

    expect(result).toEqual([[null]])
  })
})
