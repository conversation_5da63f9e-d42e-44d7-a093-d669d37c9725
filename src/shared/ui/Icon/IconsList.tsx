import { lazy } from 'react'

const whiteLogo = lazy(() => import('shared/assets/icons/WhiteLogo.svg?react'))
const noData = lazy(() => import('shared/assets/icons/NoData.svg?react'))
const arrow = lazy(() => import('shared/assets/icons/Arrow.svg?react'))
const arrowLeft = lazy(() => import('shared/assets/icons/ArrowLeft.svg?react'))
const arrowTop = lazy(() => import('shared/assets/icons/ArrowTop.svg?react'))
const arrowBottom = lazy(() => import('shared/assets/icons/ArrowBottom.svg?react'))
const nsi = lazy(() => import('shared/assets/icons/Nsi.svg?react'))
const openLock = lazy(() => import('shared/assets/icons/OpenLock.svg?react'))
const calcModel = lazy(() => import('shared/assets/icons/CalcModel.svg?react'))
const calc = lazy(() => import('shared/assets/icons/Calc.svg?react'))
const administration = lazy(() => import('shared/assets/icons/Administration.svg?react'))
const arrowSmall = lazy(() => import('shared/assets/icons/ArrowSmall.svg?react'))
const logout = lazy(() => import('shared/assets/icons/Logout.svg?react'))
const upload = lazy(() => import('shared/assets/icons/Upload.svg?react'))
const update = lazy(() => import('shared/assets/icons/Update.svg?react'))
const backTime = lazy(() => import('shared/assets/icons/BackTime.svg?react'))
const journal = lazy(() => import('shared/assets/icons/Journal.svg?react'))
const calendarAdd = lazy(() => import('shared/assets/icons/CalendarAdd.svg?react'))
const calendarEdit = lazy(() => import('shared/assets/icons/CalendarEdit.svg?react'))
const calendarDone = lazy(() => import('shared/assets/icons/CalendarDone.svg?react'))
const calendarDelete = lazy(() => import('shared/assets/icons/CalendarDelete.svg?react'))
const groups = lazy(() => import('shared/assets/icons/Groups.svg?react'))
const block = lazy(() => import('shared/assets/icons/Block.svg?react'))
const exclamation = lazy(() => import('shared/assets/icons/Exclamation.svg?react'))
const star = lazy(() => import('shared/assets/icons/Star.svg?react'))
const archiveMinus = lazy(() => import('shared/assets/icons/ArchiveMinus.svg?react'))
const archivePlus = lazy(() => import('shared/assets/icons/ArchivePlus.svg?react'))

const avatarDefault = lazy(() => import('shared/assets/avatars/default.svg?react'))

const sync = lazy(() => import('shared/assets/icons/Sync.svg?react'))
const checkCircle = lazy(() => import('shared/assets/icons/CheckCircle.svg?react'))
const exclamationTriangle = lazy(() => import('shared/assets/icons/ExclamationTriangle.svg?react'))
const timesCircle = lazy(() => import('shared/assets/icons/TimesCircle.svg?react'))
const watch = lazy(() => import('shared/assets/icons/Watch.svg?react'))
const user = lazy(() => import('shared/assets/icons/User.svg?react'))
const networkWired = lazy(() => import('shared/assets/icons/NetworkWired.svg?react'))
const circlePlus = lazy(() => import('shared/assets/icons/CirclePlus.svg?react'))
const closeLock = lazy(() => import('shared/assets/icons/CloseLock.svg?react'))
const circleMinus = lazy(() => import('shared/assets/icons/CircleMinus.svg?react'))
const circleInfo = lazy(() => import('shared/assets/icons/CircleInfo.svg?react'))

const plant = lazy(() => import('shared/assets/icons/Plant.svg?react'))
const department = lazy(() => import('shared/assets/icons/Department.svg?react'))
const generator = lazy(() => import('shared/assets/icons/Generator.svg?react'))
const rge = lazy(() => import('shared/assets/icons/Rge.svg?react'))
const plus = lazy(() => import('shared/assets/icons/Plus.svg?react'))
const points = lazy(() => import('shared/assets/icons/Points.svg?react'))
const trash = lazy(() => import('shared/assets/icons/Trash.svg?react'))

const search = lazy(() => import('shared/assets/icons/Search.svg?react'))
const sortAlphabeat = lazy(() => import('shared/assets/icons/SortAlphabeat.svg?react'))
const sortCustom = lazy(() => import('shared/assets/icons/SortCustom.svg?react'))
const settings = lazy(() => import('shared/assets/icons/Settings.svg?react'))
const view = lazy(() => import('shared/assets/icons/View.svg?react'))
const information = lazy(() => import('shared/assets/icons/Information.svg?react'))
const book = lazy(() => import('shared/assets/icons/Book.svg?react'))
const ghost = lazy(() => import('shared/assets/icons/Ghost.svg?react'))
const dragAndDrop = lazy(() => import('shared/assets/icons/DragAndDrop.svg?react'))
const time = lazy(() => import('shared/assets/icons/Time.svg?react'))
const loadStation = lazy(() => import('shared/assets/icons/LoadStation.svg?react'))
const history = lazy(() => import('shared/assets/icons/History.svg?react'))
const leaf = lazy(() => import('shared/assets/icons/Leaf.svg?react'))
const lightning = lazy(() => import('shared/assets/icons/Lightning.svg?react'))
const chartBar = lazy(() => import('shared/assets/icons/ChartBar.svg?react'))
const chartLine = lazy(() => import('shared/assets/icons/ChartLine.svg?react'))
const noView = lazy(() => import('shared/assets/icons/NoView.svg?react'))

const calculationOfPermissibleZones = lazy(() => import('shared/assets/icons/CalculationOfPermissibleZones.svg?react'))
const calculationOfThePlannedGenerationSchedule = lazy(
  () => import('shared/assets/icons/CalculationOfThePlannedGenerationSchedule.svg?react'),
)
const enteringIntoAnAcceptableAreaRelativeToTheConsumptionSchedule = lazy(
  () => import('shared/assets/icons/EnteringIntoAnAcceptableAreaRelativeToTheConsumptionSchedule.svg?react'),
)
const enteringIntoAnAcceptableAreaRelativeToTheNearestBorder = lazy(
  () => import('shared/assets/icons/EnteringIntoAnAcceptableAreaRelativeToTheNearestBorder.svg?react'),
)
const loadData = lazy(() => import('shared/assets/icons/LoadData.svg?react'))
const loadTelemetry = lazy(() => import('shared/assets/icons/LoadTelemetry.svg?react'))
const isp = lazy(() => import('shared/assets/icons/ISP.svg?react'))
const modes = lazy(() => import('shared/assets/icons/Modes.svg?react'))
const rm = lazy(() => import('shared/assets/icons/RM.svg?react'))
const gesChart = lazy(() => import('shared/assets/icons/GesChart.svg?react'))
const exit = lazy(() => import('shared/assets/icons/Exit.svg?react'))
const reports = lazy(() => import('shared/assets/icons/Reports.svg?react'))
const unloading = lazy(() => import('shared/assets/icons/Unloading.svg?react'))
const newsletter = lazy(() => import('shared/assets/icons/Newsletter.svg?react'))
const calcRGE = lazy(() => import('shared/assets/icons/CalcRGE.svg?react'))
const warning = lazy(() => import('shared/assets/icons/warning.svg?react'))
const error = lazy(() => import('shared/assets/icons/error.svg?react'))
const journals = lazy(() => import('shared/assets/icons/MenuBook.svg?react'))
const excel = lazy(() => import('shared/assets/icons/Excel.svg?react'))
const modesWrite = lazy(() => import('shared/assets/icons/ModesWrite.svg?react'))
const arrowForDatePicker = lazy(() => import('shared/assets/icons/ArrowForDatePicker.svg?react'))
const doubleArrowForDatePicker = lazy(() => import('shared/assets/icons/DoubleArrowForDatePicker.svg?react'))
const backArrowForDatePicker = lazy(() => import('shared/assets/icons/BackArrowForDatePicker.svg?react'))
const backDoubleArrowForDatePicker = lazy(() => import('shared/assets/icons/BackDoubleArrowForDatePicker.svg?react'))
const wave = lazy(() => import('shared/assets/icons/Wave.svg?react'))
const bulb = lazy(() => import('shared/assets/icons/Bulb.svg?react'))
const model = lazy(() => import('shared/assets/icons/Model.svg?react'))
const accumulator = lazy(() => import('shared/assets/icons/Accumulator.svg?react'))
const max = lazy(() => import('shared/assets/icons/Max.svg?react'))
const clear = lazy(() => import('shared/assets/icons/Clear.svg?react'))
const bigArrowLeft = lazy(() => import('shared/assets/icons/BigArrowLeft.svg?react'))
const snowflake = lazy(() => import('shared/assets/icons/Snowflake.svg?react'))
const sun = lazy(() => import('shared/assets/icons/Sun.svg?react'))
const close = lazy(() => import('shared/assets/icons/Close.svg?react'))

export {
  accumulator,
  administration,
  archiveMinus,
  archivePlus,
  arrow,
  arrowBottom,
  arrowForDatePicker,
  arrowLeft,
  arrowSmall,
  arrowTop,
  avatarDefault,
  backArrowForDatePicker,
  backDoubleArrowForDatePicker,
  backTime,
  bigArrowLeft,
  block,
  book,
  bulb,
  calc,
  calcModel,
  calcRGE,
  calculationOfPermissibleZones,
  calculationOfThePlannedGenerationSchedule,
  calendarAdd,
  calendarDelete,
  calendarDone,
  calendarEdit,
  chartBar,
  chartLine,
  checkCircle,
  circleInfo,
  circleMinus,
  circlePlus,
  clear,
  close,
  closeLock,
  department,
  doubleArrowForDatePicker,
  dragAndDrop,
  enteringIntoAnAcceptableAreaRelativeToTheConsumptionSchedule,
  enteringIntoAnAcceptableAreaRelativeToTheNearestBorder,
  error,
  excel,
  exclamation,
  exclamationTriangle,
  exit,
  generator,
  gesChart,
  ghost,
  groups,
  history,
  information,
  isp,
  journal,
  journals,
  leaf,
  lightning,
  loadData,
  loadStation,
  loadTelemetry,
  logout,
  max,
  model,
  modes,
  modesWrite,
  networkWired,
  newsletter,
  noData,
  noView,
  nsi,
  openLock,
  plant,
  plus,
  points,
  reports,
  rge,
  rm,
  search,
  settings,
  snowflake,
  sortAlphabeat,
  sortCustom,
  star,
  sun,
  sync,
  time,
  timesCircle,
  trash,
  unloading,
  update,
  upload,
  user,
  view,
  warning,
  watch,
  wave,
  whiteLogo,
}
