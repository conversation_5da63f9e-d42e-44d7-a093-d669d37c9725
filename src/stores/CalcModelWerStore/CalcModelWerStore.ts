import { addDays, format, isAfter, isValid, startOfDay } from 'date-fns'
import { makeAutoObservable, runInAction } from 'mobx'
import { WerCalculationJournalStore } from 'pages/WerCalculationPage/ui/CalculationJournal/model/WerCalculationJournalStore'
import api from 'shared/api/index'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { IPlantForLeftMenu } from 'stores/CalcModelStore'
import { WerListOfCascadesStore } from 'stores/CalcModelWerStore/WerListOfCascadesStore/WerListOfCascadesStore'
import { WerListOfStationsStore } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerListOfStationsStore.ts'
import { type RootStore } from 'stores/RootStore'

import { ICalcModelWerStore } from './CalcModelWerStore.types'
import { WerRunningTimesStore } from './WerRunningTimesStore'

export class CalcModelWerStore implements ICalcModelWerStore {
  rootStore: ICalcModelWerStore['rootStore']
  listOfStationsStore: ICalcModelWerStore['listOfStationsStore']
  listOfCascadesStore: ICalcModelWerStore['listOfCascadesStore']
  runningTimesStore: ICalcModelWerStore['runningTimesStore']
  calculationJournal: ICalcModelWerStore['calculationJournal']
  plants: ICalcModelWerStore['plants'] = []
  selectedPlant: ICalcModelWerStore['selectedPlant'] = null
  date: ICalcModelWerStore['date'] = addDays(new Date(), 1)
  params: ICalcModelWerStore['params'] = []
  history: ICalcModelWerStore['history'] = []
  isLoadingPlants: ICalcModelWerStore['isLoadingPlants'] = false
  depPlants: ICalcModelWerStore['depPlants'] = []
  lookedPlants: ICalcModelWerStore['lookedPlants'] = []
  currentAbortController: ICalcModelWerStore['currentAbortController'] = null

  constructor(
    rootStore: RootStore,
    listOfStationsStore: WerListOfStationsStore,
    listOfCascadesStore: WerListOfCascadesStore,
    runningTimesStore: WerRunningTimesStore,
    calculationJournal: WerCalculationJournalStore,
  ) {
    this.rootStore = rootStore
    this.listOfStationsStore = listOfStationsStore
    this.listOfCascadesStore = listOfCascadesStore
    this.runningTimesStore = runningTimesStore
    this.calculationJournal = calculationJournal
    makeAutoObservable(this)
  }

  setSelectedPlant: ICalcModelWerStore['setSelectedPlant'] = (selectedPlantValue) => {
    const selectedPlant = selectedPlantValue ? this.plants.find((el) => el.value === selectedPlantValue) : null
    this.selectedPlant = selectedPlant ?? null
    this.listOfStationsStore.characteristicsStore.reservoirVolumeStore.isEditRows = false
    this.listOfStationsStore.characteristicsStore.tailraceStore.isEditRows = false
    this.listOfStationsStore.characteristicsStore.tailraceStore.actualData = []
    this.listOfStationsStore.characteristicsStore.specificConsumptionStore.isEditRows = false
    this.listOfStationsStore.characteristicsStore.specificConsumptionStore.actualData = []
  }

  setDate: ICalcModelWerStore['setDate'] = (date) => {
    if (!isValid(date)) return
    this.date = date
  }

  get formattedDate(): ICalcModelWerStore['formattedDate'] {
    return format(this.date, 'yyyy-MM-dd')
  }

  get isSelectedDateEditable(): ICalcModelWerStore['isSelectedDateEditable'] {
    const today = startOfDay(new Date())
    const selectedDay = startOfDay(this.date)

    return isAfter(selectedDay, today)
  }

  get isSelectedPlantViewOnly(): ICalcModelWerStore['isSelectedPlantViewOnly'] {
    return this.selectedPlant ? this.selectedPlant.viewOnly : false
  }

  resetPlants: ICalcModelWerStore['resetPlants'] = () => {
    this.plants = []
  }

  prepareDepPlants: ICalcModelWerStore['prepareDepPlants'] = (arr) => {
    return arr.map((el) => {
      const disabledChecked = el.type !== 'PLANT'
      const visibleAndDisabled = el.isPlanned
      const children = el?.children?.length > 0 ? this.prepareDepPlants(el.children) : []
      const tabId = generateUUID()
      if (el.isLooked) this.lookedPlants.push(tabId)

      return {
        ...el,
        tabId,
        children,
        visibleAndDisabled,
        disabledChecked,
        rowColor: el.type === 'PLANT' && el.isPlanned ? 'gray' : '',
        prompt: el.type === 'PLANT' && el.isPlanned ? 'Планируемая' : '',
      }
    })
  }

  resetLookedPlants: ICalcModelWerStore['resetLookedPlants'] = () => {
    this.lookedPlants = []
  }

  initModalAdd: ICalcModelWerStore['initModalAdd'] = async () => {
    try {
      const depPlants = await api.calcModelWerManager.getDepPlants()
      this.depPlants = this.prepareDepPlants(depPlants)
    } catch (e) {
      console.log(e)
    }
  }

  saveDepPlants: ICalcModelWerStore['saveDepPlants'] = async (res) => {
    try {
      await api.calcModelWerManager.saveDepPlants(res)
    } catch (e) {
      console.log(e)
    }
  }

  changeSortType: ICalcModelWerStore['changeSortType'] = async (sortByOrder, date, idStation?) => {
    if (this.currentAbortController) this.currentAbortController.abort()

    const abortController = new AbortController()
    this.currentAbortController = abortController
    const signal = this.currentAbortController.signal

    this.isLoadingPlants = true

    try {
      const plants = await api.calcModelWerManager.getPlanPlants(sortByOrder, date, signal)

      if (this.currentAbortController !== abortController) return

      runInAction(() => {
        this.plants = [
          ...plants.map((el) => ({
            ...el,
            value: el.plantId,
            label: el.name,
            icon: el.viewOnly ? 'view' : 'settings',
          })),
        ] as IPlantForLeftMenu[]
      })
      if (idStation) {
        return !this.plants.some((el) => el.plantId === idStation)
      } else {
        return undefined
      }
    } catch (e) {
      console.log(e)
    } finally {
      if (this.currentAbortController === abortController) {
        this.currentAbortController = null
      }
      this.isLoadingPlants = false
    }
  }

  setCustomSortLeft: ICalcModelWerStore['setCustomSortLeft'] = async (res) => {
    try {
      const final = res.map((el: string) => Number(el))
      await api.calcModelWerManager.saveCustomSort(final)
    } catch (e) {
      console.log(e)
    }
  }
}
