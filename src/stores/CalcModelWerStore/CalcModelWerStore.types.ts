import {
  getDepPlantsOutput,
  IGetHistoryParamsOutput,
  IParametersConfigsOutput,
  ISaveDepPlantsInput,
  ValueConfigsOutputWithGroups,
} from 'entities/api/calcModelPage.entities.ts'
import { WerCalculationJournalStore } from 'pages/WerCalculationPage/ui/CalculationJournal/model/WerCalculationJournalStore'
import { WerListOfCascadesStore } from 'src/stores/CalcModelWerStore/WerListOfCascadesStore'
import { WerListOfStationsStore } from 'src/stores/CalcModelWerStore/WerListOfStationsStore'
import { IDepPlants, IPlantForLeftMenu } from 'stores/CalcModelStore'
import { RootStore } from 'stores/RootStore.ts'

import { WerRunningTimesStore } from './WerRunningTimesStore'

export interface ICalcModelWerStore {
  rootStore: RootStore
  listOfStationsStore: WerListOfStationsStore
  listOfCascadesStore: WerListOfCascadesStore
  runningTimesStore: WerRunningTimesStore
  calculationJournal: WerCalculationJournalStore
  plants: IPlantForLeftMenu[]
  selectedPlant: IPlantForLeftMenu | null
  date: Date
  params: IParametersConfigsOutput<number | string | ValueConfigsOutputWithGroups>[]
  history: IGetHistoryParamsOutput[]
  isLoadingPlants: boolean
  depPlants: IDepPlants[]
  lookedPlants: string[]
  currentAbortController: AbortController | null

  setSelectedPlant: (selectedPlantValue: IPlantForLeftMenu['value'] | null) => void
  setDate: (date: Date) => void
  resetPlants: () => void
  formattedDate: string
  isSelectedDateEditable: boolean
  isSelectedPlantViewOnly: boolean
  prepareDepPlants: (arr: getDepPlantsOutput[]) => IDepPlants[]
  resetLookedPlants: () => void
  initModalAdd: () => Promise<void>
  saveDepPlants: (res: ISaveDepPlantsInput) => Promise<void>
  changeSortType: (sortByOrder: boolean, date: string, idStation?: number) => Promise<boolean | undefined>
  setCustomSortLeft: (res: string[]) => Promise<void>
}
